# StepStyle Payment Integration

This project integrates multiple payment gateways with the StepStyle e-commerce website:

1. **Stripe Payment Gateway** - For international card payments
2. **UPI Payment Gateway** - For Indian UPI payments (Google Pay, PhonePe, Paytm)

## Setup Instructions

1. Install dependencies:
   ```
   npm install
   ```

2. Set up your Stripe account:
   - Sign up for a Stripe account at [stripe.com](https://stripe.com)
   - Get your API keys from the Stripe Dashboard
   - Replace `YOUR_SECRET_KEY` in `.env` file with your actual Stripe secret key
   - The publishable key `pk_test_51RMCvhFPQAtPKLAIzO3Y7WjVErTuQI2BMKbyLl8uCHiCqBtZuVQEmPCiOOCqB3UnuLhYw2K0s2JnecLutL821Akb00zvhXh8HB` has been added to `checkout.js`

3. Start the server:
   ```
   npm start
   ```

4. The server will run on port 4242. Make sure this port is available.

5. Open your website in a browser and test the checkout process.

## Files Overview

### Server Files
- `server.js`: Express server that handles both Stripe and UPI payment processing
- `package.json`: Node.js dependencies and scripts
- `.env`: Environment variables for API keys and configuration

### Payment Response Pages
- `success.html`: Page displayed after successful Stripe payment
- `cancel.html`: Page displayed when Stripe payment is cancelled
- `upi-status.html`: Page that verifies and displays UPI payment status

### Test Pages
- `stripe-test.html`: Standalone test page for Stripe checkout
- `upi-test.html`: Standalone test page for UPI payments

### Frontend Integration
- `Frontend/src/js/checkout.js`: Frontend code that integrates with both payment gateways
- `Frontend/Pages/checkout.html`: Checkout page with payment options

## How It Works

### Stripe Payment Flow

1. When a user clicks "Pay with Card" or "Pay with Stripe Checkout" on the checkout page, the frontend sends a request to the server with the cart items.
2. The server creates a Stripe checkout session and returns the session ID.
3. The frontend redirects the user to Stripe's hosted checkout page.
4. After payment, Stripe redirects the user back to the success or cancel page.
5. The success page clears the cart and shows order confirmation.

### UPI Payment Flow

1. When a user enters their UPI ID and clicks "Pay with UPI" on the checkout page, the frontend sends a request to the server with the cart items and UPI details.
2. The server creates a UPI payment intent and generates a UPI payment URL.
3. The frontend opens the UPI payment URL, which launches the selected UPI app (Google Pay, PhonePe, or Paytm).
4. The user completes the payment in the UPI app.
5. The frontend redirects to the UPI status page, which verifies the payment status with the server.
6. The UPI status page shows the payment result and clears the cart on successful payment.

## Webhook Integration

For production use, you should set up Stripe webhooks to handle asynchronous events like payment confirmations. The server includes a webhook endpoint at `/webhook` that you can configure in your Stripe Dashboard.

## Testing

### Testing Stripe Payments
You can test the Stripe integration using Stripe's test cards:
- Card number: 4242 4242 4242 4242 (Success)
- Card number: 4000 0000 0000 9995 (3D Secure)
- Card number: 4000 0000 0000 0002 (Declined)
- Expiry date: Any future date
- CVC: Any 3 digits
- ZIP: Any 5 digits

### Testing UPI Payments
You can test the UPI integration by:
1. Entering any valid UPI ID format (e.g., `yourname@upi`)
2. Selecting a UPI app (Google Pay, PhonePe, or Paytm)
3. Clicking "Pay with UPI"
4. The app will attempt to open the selected UPI app
5. For testing purposes, the payment will be automatically marked as successful after verification

## Troubleshooting

### General Issues
- If you encounter CORS issues, make sure the server's CORS configuration matches your frontend domain.
- Check the browser console and server logs for error messages.
- Make sure the server is running on port 4242 and is accessible from your frontend.

### Stripe Issues
- Verify that your Stripe API keys are correct and have the necessary permissions.
- Check that the Stripe.js library is properly loaded in your checkout page.
- Ensure you're using the correct test card numbers for testing.

### UPI Issues
- If UPI apps don't open, make sure you're testing on a mobile device or emulator.
- For desktop testing, the UPI URL will open in a new tab but won't launch any app.
- Check that your UPI ID format is valid (should contain the '@' symbol).
- Verify that the server is correctly generating the UPI payment URL.

## Production Considerations

For a production environment:

1. **Secure your API keys**: Use environment variables and never expose them in client-side code.
2. **Set up proper error handling**: Implement comprehensive error handling and logging.
3. **Implement webhook verification**: For both Stripe and UPI payment status updates.
4. **Add database storage**: Store payment records in a database for persistence and reporting.
5. **Implement proper authentication**: Secure payment endpoints with authentication.
6. **Set up HTTPS**: Ensure all payment communications are encrypted with HTTPS.
