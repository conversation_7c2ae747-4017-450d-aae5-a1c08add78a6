<!DOCTYPE html>
<html>
  <head>
    <title>Stripe Checkout Test - StepStyle</title>
    <script src="https://js.stripe.com/v3/"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
      body {
        font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        color: #333;
      }
      .container {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        padding: 40px;
        text-align: center;
        max-width: 500px;
        width: 90%;
      }
      h1 {
        font-size: 28px;
        margin-bottom: 15px;
        font-weight: 600;
      }
      p {
        font-size: 16px;
        line-height: 1.6;
        color: #666;
        margin-bottom: 30px;
      }
      .checkout-button {
        background-color: #635BFF;
        color: white;
        border: none;
        padding: 14px 30px;
        border-radius: 30px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      .checkout-button:hover {
        background-color: #4b44c9;
        transform: translateY(-2px);
      }
      .checkout-button i {
        margin-right: 8px;
        font-size: 20px;
      }
      .back-link {
        display: inline-block;
        margin-top: 20px;
        color: #666;
        text-decoration: none;
        font-size: 14px;
      }
      .back-link:hover {
        color: #111;
        text-decoration: underline;
      }
      .loading {
        display: none;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
      .loading i {
        animation: spin 1s linear infinite;
        font-size: 24px;
        margin-right: 10px;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      .logo {
        margin-bottom: 20px;
      }
      .logo img {
        height: 60px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">
        <img src="./assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo">
      </div>
      <h1>Stripe Checkout Test</h1>
      <p>Click the button below to test the Stripe checkout integration.</p>
      
      <button id="checkout-button" class="checkout-button">
        <i class="ri-bank-card-line"></i>
        Pay with Stripe
      </button>
      
      <div id="loading" class="loading">
        <i class="ri-loader-line"></i>
        <span>Processing your payment...</span>
      </div>
      
      <a href="index.html" class="back-link">← Return to home page</a>
    </div>

    <script>
      // Initialize Stripe with your publishable key
      const stripe = Stripe('pk_test_51RMCvhFPQAtPKLAIzO3Y7WjVErTuQI2BMKbyLl8uCHiCqBtZuVQEmPCiOOCqB3UnuLhYw2K0s2JnecLutL821Akb00zvhXh8HB');
      
      // Handle checkout button click
      document.getElementById('checkout-button').addEventListener('click', async () => {
        // Show loading indicator
        const loadingElement = document.getElementById('loading');
        const checkoutButton = document.getElementById('checkout-button');
        
        loadingElement.style.display = 'flex';
        checkoutButton.disabled = true;
        
        try {
          // Create checkout session
          const response = await fetch('http://localhost:4242/create-checkout-session', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              items: [
                {
                  name: 'StepStyle Premium Shoes',
                  description: 'Premium quality shoes from StepStyle',
                  currentPrice: 4999,
                  quantity: 1
                }
              ]
            }),
          });
          
          const session = await response.json();
          
          // Redirect to Stripe Checkout
          const result = await stripe.redirectToCheckout({
            sessionId: session.id,
          });
          
          if (result.error) {
            // If redirectToCheckout fails due to a browser or network error
            console.error(result.error.message);
            alert('Payment failed: ' + result.error.message);
            
            // Hide loading indicator and re-enable button
            loadingElement.style.display = 'none';
            checkoutButton.disabled = false;
          }
        } catch (error) {
          console.error('Error:', error);
          alert('An error occurred. Please try again.');
          
          // Hide loading indicator and re-enable button
          loadingElement.style.display = 'none';
          checkoutButton.disabled = false;
        }
      });
    </script>
  </body>
</html>
