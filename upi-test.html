<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UPI Payment Test - StepStyle</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
        body {
            font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: #555;
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="tel"]:focus {
            border-color: #111;
            outline: none;
        }
        .upi-apps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .upi-app {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 15px;
            cursor: pointer;
        }
        .upi-app input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        .upi-app-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #f1f1f1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            transition: all 0.3s;
        }
        .upi-app-icon img {
            width: 35px;
            height: 35px;
            object-fit: contain;
        }
        .upi-app input[type="radio"]:checked + .upi-app-icon {
            background-color: #e6f7ff;
            box-shadow: 0 0 0 2px #1890ff;
        }
        .upi-app span {
            font-size: 14px;
            color: #666;
        }
        .button {
            background-color: #111;
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .button:hover {
            background-color: #333;
            transform: translateY(-2px);
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .button i {
            margin-right: 8px;
            font-size: 20px;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            color: #111;
            text-decoration: underline;
        }
        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }
        .loading i {
            animation: spin 1s linear infinite;
            font-size: 24px;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            height: 60px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="./assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo">
        </div>
        <h1>UPI Payment Test</h1>
        <p>Enter your UPI ID and select your preferred UPI app to test the payment integration.</p>
        
        <div class="form-group">
            <label for="upiId">UPI ID</label>
            <input type="text" id="upiId" placeholder="yourname@upi" required>
        </div>
        
        <div class="form-group">
            <label>Select UPI App</label>
            <div class="upi-apps">
                <label class="upi-app">
                    <input type="radio" name="upiApp" value="gpay" checked>
                    <div class="upi-app-icon">
                        <img src="./assets/images/google-pay.svg" alt="Google Pay">
                    </div>
                    <span>Google Pay</span>
                </label>
                <label class="upi-app">
                    <input type="radio" name="upiApp" value="phonepe">
                    <div class="upi-app-icon">
                        <img src="./assets/images/phonepe-icon.svg" alt="PhonePe">
                    </div>
                    <span>PhonePe</span>
                </label>
                <label class="upi-app">
                    <input type="radio" name="upiApp" value="paytm">
                    <div class="upi-app-icon">
                        <img src="./assets/images/paytm-icon.svg" alt="Paytm">
                    </div>
                    <span>Paytm</span>
                </label>
            </div>
        </div>
        
        <button id="pay-button" class="button">
            <i class="ri-bank-card-line"></i>
            Pay with UPI
        </button>
        
        <div id="loading" class="loading">
            <i class="ri-loader-line"></i>
            <span>Processing your payment...</span>
        </div>
        
        <a href="index.html" class="back-link">← Return to home page</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const payButton = document.getElementById('pay-button');
            const upiIdInput = document.getElementById('upiId');
            const loadingElement = document.getElementById('loading');
            
            // Enable/disable pay button based on UPI ID
            upiIdInput.addEventListener('input', function() {
                const upiId = this.value.trim();
                payButton.disabled = !upiId || !upiId.includes('@');
            });
            
            // Initialize button state
            payButton.disabled = true;
            
            // Handle pay button click
            payButton.addEventListener('click', async function() {
                const upiId = upiIdInput.value.trim();
                const upiApp = document.querySelector('input[name="upiApp"]:checked').value;
                
                if (!upiId || !upiId.includes('@')) {
                    alert('Please enter a valid UPI ID (e.g., yourname@upi)');
                    return;
                }
                
                // Show loading indicator
                loadingElement.style.display = 'flex';
                payButton.disabled = true;
                
                try {
                    // Create UPI payment
                    const response = await fetch('http://localhost:4242/create-upi-payment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            items: [
                                {
                                    name: 'StepStyle Premium Shoes',
                                    description: 'Premium quality shoes from StepStyle',
                                    currentPrice: 4999,
                                    quantity: 1
                                }
                            ],
                            upiId: upiId,
                            upiApp: upiApp,
                            customerName: 'Test Customer',
                            customerEmail: '<EMAIL>',
                            customerPhone: '9876543210'
                        }),
                    });
                    
                    const paymentData = await response.json();
                    
                    if (!paymentData.success) {
                        throw new Error(paymentData.error || 'Failed to create UPI payment');
                    }
                    
                    console.log('UPI Payment created:', paymentData);
                    
                    // Save payment ID to localStorage for verification
                    localStorage.setItem('stepstyle-upi-payment', JSON.stringify({
                        paymentId: paymentData.paymentId,
                        amount: paymentData.amount,
                        timestamp: new Date().toISOString()
                    }));
                    
                    // Open the UPI payment URL in a new window/tab
                    window.open(paymentData.upiPaymentUrl, '_blank');
                    
                    // After a short delay, redirect to the UPI status page
                    setTimeout(() => {
                        window.location.href = `/upi-status.html?paymentId=${paymentData.paymentId}`;
                    }, 2000);
                } catch (error) {
                    console.error('Error creating UPI payment:', error);
                    alert('Error: ' + error.message);
                    
                    // Hide loading indicator and re-enable button
                    loadingElement.style.display = 'none';
                    payButton.disabled = false;
                }
            });
        });
    </script>
</body>
</html>
