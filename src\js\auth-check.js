/**
 * Authentication check for StepStyle website
 * This script checks if the user is authenticated and redirects to login page if not
 */

// Function to check if user is authenticated
function isAuthenticated() {
    const token = localStorage.getItem('auth-token');
    return !!token; // Convert to boolean
}

// Function to handle user authentication UI
function updateAuthUI() {
    const userIcon = document.querySelector('.ph-user-circle');
    
    if (userIcon) {
        if (isAuthenticated()) {
            // User is logged in, show user dropdown when clicked
            userIcon.addEventListener('click', toggleUserDropdown);
        } else {
            // User is not logged in, redirect to login page when clicked
            userIcon.addEventListener('click', () => {
                window.location.href = getLoginPagePath();
            });
        }
    }
}

// Function to toggle user dropdown
function toggleUserDropdown() {
    // Check if dropdown already exists
    let dropdown = document.querySelector('.user-dropdown');
    
    if (dropdown) {
        // Toggle visibility
        dropdown.classList.toggle('active');
        return;
    }
    
    // Create dropdown
    dropdown = document.createElement('div');
    dropdown.className = 'user-dropdown active';
    
    // Add logout option
    const logoutOption = document.createElement('div');
    logoutOption.className = 'dropdown-option';
    logoutOption.innerHTML = '<i class="ri-logout-box-line"></i> Logout';
    logoutOption.addEventListener('click', handleLogout);
    
    // Add options to dropdown
    dropdown.appendChild(logoutOption);
    
    // Add dropdown to DOM
    const userIcon = document.querySelector('.ph-user-circle');
    userIcon.parentNode.style.position = 'relative';
    userIcon.parentNode.appendChild(dropdown);
    
    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.user-dropdown') && !e.target.closest('.ph-user-circle')) {
            const dropdown = document.querySelector('.user-dropdown');
            if (dropdown) {
                dropdown.classList.remove('active');
            }
        }
    });
}

// Function to handle logout
function handleLogout() {
    // Remove token from localStorage
    localStorage.removeItem('auth-token');
    
    // Redirect to login page
    window.location.href = getLoginPagePath();
}

// Function to get the path to the login page based on current location
function getLoginPagePath() {
    // Check if we're in the root directory or in a subdirectory
    const path = window.location.pathname;
    
    if (path.includes('/Pages/')) {
        return './login.html';
    } else {
        return './Pages/login.html';
    }
}

// Function to check authentication and redirect if needed
function checkAuthAndRedirect() {
    // Skip check for index.html and login.html
    const path = window.location.pathname;
    
    if (path.endsWith('index.html') || path === '/' || path.endsWith('login.html')) {
        return;
    }
    
    // For all other pages, check authentication
    if (!isAuthenticated()) {
        // Save the current URL to redirect back after login
        localStorage.setItem('auth-redirect', window.location.href);
        
        // Redirect to login page
        window.location.href = getLoginPagePath();
    }
}

// Run on page load
document.addEventListener('DOMContentLoaded', () => {
    // Update UI based on authentication status
    updateAuthUI();
    
    // Check authentication and redirect if needed
    checkAuthAndRedirect();
    
    // Add styles for user dropdown
    addDropdownStyles();
});

// Function to add dropdown styles
function addDropdownStyles() {
    // Create style element
    const style = document.createElement('style');
    
    // Add CSS for dropdown
    style.textContent = `
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 10px 0;
            min-width: 150px;
            z-index: 1000;
            display: none;
        }
        
        .user-dropdown.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        .dropdown-option {
            padding: 10px 15px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .dropdown-option:hover {
            background-color: #f5f5f5;
        }
        
        .dropdown-option i {
            margin-right: 10px;
            font-size: 18px;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    
    // Add style to head
    document.head.appendChild(style);
}

// Export functions for use in other files
export { isAuthenticated, updateAuthUI, handleLogout };
