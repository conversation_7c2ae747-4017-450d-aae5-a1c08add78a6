<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StepStyle - Login & Register</title>
    <link rel="shortcut icon" href="../Frontend/assets/images/favicon.ico" type="image/x-icon" />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="new-login-design.css">
</head>
<body>
    <div class="login-container">
        <!-- Left side with image and branding -->
        <div class="left-side">
            <div class="brand-header">
                <h2>StepStyle</h2>
            </div>

            <div class="shoe-image">
                <img src="../Frontend/assets/images/nike-airForce.png" alt="Nike Air Force" />
            </div>

            <div class="brand-info">
                <div class="brand-logo">
                    <img src="../Frontend/assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo" />
                    <div class="brand-details">
                        <h3>StepStyle</h3>
                        <p>Footwear & Fashion</p>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="action-btn"><i class="ri-heart-line"></i></button>
                    <button class="action-btn"><i class="ri-share-line"></i></button>
                </div>
            </div>
        </div>

        <!-- Right side with forms -->
        <div class="right-side">
            <div class="form-header">
                <h1>STEPSTYLE</h1>
                <div class="language-selector">
                    <span>EN</span>
                    <i class="ri-arrow-down-s-line"></i>
                </div>
            </div>

            <!-- Login Form -->
            <div class="form-content active" id="login-form">
                <h2>Welcome Back</h2>
                <p class="form-subtitle">Sign in to continue your shopping experience</p>

                <form id="login-form-element">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <div class="password-container">
                            <input type="password" id="login-password" placeholder="••••••" value="password123">
                            <button type="button" class="password-toggle">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="button" class="google-btn">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" />
                        Login with Google
                    </button>

                    <button type="submit" class="login-btn">Login</button>

                    <p class="switch-form">
                        Don't have an account? <a href="#" class="switch-link" data-target="register">Sign up</a>
                    </p>
                </form>
            </div>

            <!-- Register Form -->
            <div class="form-content" id="register-form">
                <h2>Create Account</h2>
                <p class="form-subtitle">Join StepStyle for exclusive offers and more</p>

                <form id="register-form-element">
                    <div class="form-group">
                        <label for="register-name">Full Name</label>
                        <input type="text" id="register-name" placeholder="vansh sharma" value="vansh sharma">
                    </div>

                    <div class="form-group">
                        <label for="register-email">Email</label>
                        <input type="email" id="register-email" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <div class="password-container">
                            <input type="password" id="register-password" placeholder="••••••" value="password123">
                            <button type="button" class="password-toggle">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="terms" checked>
                        <label for="terms">I agree to the <a href="#">Terms & Conditions</a></label>
                    </div>

                    <button type="submit" class="create-btn">Create Account</button>

                    <p class="switch-form">
                        Already have an account? <a href="#" class="switch-link" data-target="login">Sign in</a>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast-notification">
        <i class="ri-check-line toast-icon"></i>
        <span class="toast-message">Welcome to StepStyle!</span>
    </div>

    <script src="new-login-design.js"></script>
</body>
</html>
