<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StepStyle - Login & Register</title>
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.css"
    />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../src/styles/login.css">
</head>
<body>
    <main class="modern-login">
        <div class="login-container">
            <!-- Left side with image -->
            <div class="login-image-side">
                <div class="top-nav">
                    <div class="selected-works">StepStyle</div>
                </div>

                <div class="profile-section">
                    <div class="profile-info">
                        <img src="../assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo" class="profile-pic">
                        <div class="profile-text">
                            <h3>StepStyle</h3>
                            <p>Footwear & Fashion</p>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button class="action-btn"><i class="ri-heart-line"></i></button>
                        <button class="action-btn"><i class="ri-share-line"></i></button>
                    </div>
                </div>
            </div>

            <!-- Right side with forms -->
            <div class="login-form-side">
                <div class="form-header">
                    <h1 class="brand-name">STEPSTYLE</h1>
                    <div class="language-selector">
                        <span>EN</span>
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>

                <!-- Login Form -->
                <div class="form-content active" id="login-form">
                    <h2>Welcome Back</h2>
                    <p class="form-subtitle">Sign in to continue your shopping experience</p>

                    <form id="login">
                        <div class="form-group">
                            <label for="login-email">Email</label>
                            <input type="email" id="login-email" placeholder="Enter your email" required>
                            <span class="error-message" id="login-email-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="login-password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="login-password" placeholder="Enter your password" required>
                                <i class="ri-eye-line toggle-password"></i>
                            </div>
                            <span class="error-message" id="login-password-error"></span>
                            <a href="#" class="forgot-password" id="forgot-password-link">Forgot password?</a>
                        </div>

                        <div class="login-options">
                            <button type="button" class="google-login-btn">
                                <img src="../assets/images/google-icon-logo-svgrepo-com.svg" alt="Google">
                                Login with Google
                            </button>

                            <button type="submit" class="auth-btn">Login</button>
                        </div>

                        <div class="signup-prompt">
                            Don't have an account? <a href="#" class="tab-link" data-tab="register">Sign up</a>
                        </div>

                        <div class="social-login">
                            <a href="#" class="social-icon"><i class="ri-facebook-fill"></i></a>
                            <a href="#" class="social-icon"><i class="ri-twitter-fill"></i></a>
                            <a href="#" class="social-icon"><i class="ri-instagram-line"></i></a>
                        </div>
                    </form>
                </div>

                <!-- Register Form -->
                <div class="form-content" id="register-form">
                    <h2>Create Account</h2>
                    <p class="form-subtitle">Join StepStyle for exclusive offers and more</p>

                    <form id="register">
                        <div class="form-group">
                            <label for="register-name">Full Name</label>
                            <input type="text" id="register-name" placeholder="Enter your full name" required>
                            <span class="error-message" id="register-name-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="register-email">Email</label>
                            <input type="email" id="register-email" placeholder="Enter your email" required>
                            <span class="error-message" id="register-email-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="register-password">Password</label>
                            <div class="password-input-container">
                                <input type="password" id="register-password" placeholder="Create a password (min. 8 characters)" required>
                                <i class="ri-eye-line toggle-password"></i>
                            </div>
                            <span class="error-message" id="register-password-error"></span>
                        </div>

                        <div class="form-options">
                            <div class="terms-conditions">
                                <input type="checkbox" id="terms" required>
                                <label for="terms">I agree to the <a href="#">Terms & Conditions</a></label>
                            </div>
                        </div>

                        <button type="submit" class="auth-btn">Create Account</button>

                        <div class="login-prompt">
                            Already have an account? <a href="#" class="tab-link" data-tab="login">Sign in</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast Notification -->
    <div class="toast-notification">
        <i class="ri-check-line toast-icon"></i>
        <span class="toast-message">Welcome to StepStyle!</span>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal" id="forgot-password-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Reset Password</h2>
            <p>Enter your email address and we'll send you instructions to reset your password.</p>

            <form id="forgot-password-form">
                <div class="form-group">
                    <label for="reset-email">Email</label>
                    <div class="input-with-icon">
                        <i class="ri-mail-line"></i>
                        <input type="email" id="reset-email" placeholder="Enter your email" required>
                    </div>
                    <span class="error-message" id="reset-email-error"></span>
                </div>

                <button type="submit" class="auth-btn">Send Reset Link</button>
            </form>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal" id="reset-password-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Create New Password</h2>
            <p>Please enter your new password below.</p>

            <form id="reset-password-form">
                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <div class="input-with-icon">
                        <i class="ri-lock-line"></i>
                        <input type="password" id="new-password" placeholder="Enter new password (min. 8 characters)" required>
                        <i class="ri-eye-line toggle-password"></i>
                    </div>
                    <span class="error-message" id="new-password-error"></span>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm Password</label>
                    <div class="input-with-icon">
                        <i class="ri-lock-line"></i>
                        <input type="password" id="confirm-password" placeholder="Confirm new password" required>
                        <i class="ri-eye-line toggle-password"></i>
                    </div>
                    <span class="error-message" id="confirm-password-error"></span>
                </div>

                <input type="hidden" id="reset-token">
                <input type="hidden" id="reset-email-value">

                <button type="submit" class="auth-btn">Reset Password</button>
            </form>
        </div>
    </div>

    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

    <!-- Custom scripts -->
    <script type="module" src="../src/js/login.js" onerror="handleModuleError()"></script>

    <!-- Script error handling -->
    <script>
        // Function to handle module script error
        function handleModuleError() {
            console.warn('Module script failed to load. Using fallback script.');

            // Load the non-module fallback script
            const fallbackScript = document.createElement('script');
            fallbackScript.src = '../src/js/login-fallback.js';
            document.body.appendChild(fallbackScript);
        }

        // Check if GSAP is loaded
        if (typeof gsap === 'undefined') {
            console.warn('GSAP is not loaded. Adding fallback animation.');
        }

        // Check if the sign-up section is visible after page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const registerForm = document.getElementById('register-form');

                // Check URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const tab = urlParams.get('tab');

                console.log('DOM loaded, checking sign-up visibility');
                console.log('Register form:', registerForm);
                console.log('URL tab parameter:', tab);

                // If the URL has tab=register but the register form is not visible, try to fix it
                if (tab === 'register' && registerForm && !registerForm.classList.contains('active')) {
                    console.log('Register form should be visible but is not. Fixing...');

                    // Remove active class from all forms
                    document.querySelectorAll('.form-content').forEach(form => form.classList.remove('active'));

                    // Add active class to register form
                    registerForm.classList.add('active');
                }

                // Add direct click handlers to the tab links as a fallback
                const tabLinks = document.querySelectorAll('.tab-link');
                tabLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        const tabId = this.getAttribute('data-tab');

                        // Hide all forms
                        document.querySelectorAll('.form-content').forEach(form => {
                            form.classList.remove('active');
                        });

                        // Show the selected form
                        const targetForm = document.getElementById(tabId + '-form');
                        if (targetForm) {
                            targetForm.classList.add('active');
                        }
                    });
                });

                // Check if backend server is running
                fetch('http://localhost:4000/api/user/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'test' })
                })
                .then(response => {
                    console.log('Backend server is running!');
                })
                .catch(error => {
                    console.error('Backend server is not running:', error);

                    // Show a message to the user
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'backend-error-message';
                    errorMessage.style.backgroundColor = '#ffebee';
                    errorMessage.style.color = '#c62828';
                    errorMessage.style.padding = '10px';
                    errorMessage.style.borderRadius = '5px';
                    errorMessage.style.margin = '10px 0';
                    errorMessage.style.textAlign = 'center';
                    errorMessage.innerHTML = `
                        <strong>Backend server is not running!</strong><br>
                        Please start the backend server by running the <code>start-backend.bat</code> file in the project root directory.
                    `;

                    // Insert at the top of the form
                    const loginForm = document.getElementById('login-form');
                    if (loginForm) {
                        const loginFormContent = loginForm.querySelector('form');
                        if (loginFormContent) {
                            loginFormContent.insertBefore(errorMessage, loginFormContent.firstChild);
                        }
                    }
                });
            }, 500); // Check after a short delay to allow scripts to run
        });
    </script>
</body>
</html>
