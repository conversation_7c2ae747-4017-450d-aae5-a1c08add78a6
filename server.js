const express = require('express');
const app = express();
const cors = require('cors');
const crypto = require('crypto');
require('dotenv').config();

// Initialize Razorpay with the key ID and secret
const Razorpay = require('razorpay');
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || 'rzp_test_YOUR_KEY_ID', // Replace with your Razorpay Key ID
  key_secret: process.env.RAZORPAY_KEY_SECRET || 'YOUR_KEY_SECRET' // Replace with your Razorpay Key Secret
});

app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files from the current directory

app.post('/create-razorpay-order', async (req, res) => {
  try {
    // Get cart items from request body
    const { items, customerInfo } = req.body || { items: [], customerInfo: {} };

    console.log('Received items for checkout:', items);

    // Calculate total amount
    let totalAmount = 0;

    if (items && items.length > 0) {
      totalAmount = items.reduce((total, item) => {
        return total + (item.currentPrice * (item.quantity || 1));
      }, 0);
    } else {
      // Default amount if no items provided
      totalAmount = 4999;
    }

    // Convert to paise (Razorpay requires amount in smallest currency unit)
    const amountInPaise = Math.round(totalAmount * 100);

    // Generate a unique receipt ID
    const receiptId = 'ORDER-' + Date.now();

    // Create order options
    const orderOptions = {
      amount: amountInPaise,
      currency: 'INR',
      receipt: receiptId,
      payment_capture: 1, // Auto-capture payment
      notes: {
        order_type: 'StepStyle Footwear',
        items_count: items ? items.length : 0
      }
    };

    // Create Razorpay order
    const order = await razorpay.orders.create(orderOptions);

    // Prepare response with order details and customer info
    const response = {
      order_id: order.id,
      currency: order.currency,
      amount: order.amount,
      receipt: order.receipt,
      key_id: razorpay.key_id,
      product_name: 'StepStyle Shoes',
      description: 'Premium quality shoes from StepStyle',
      notes: order.notes,
      prefill: {
        name: customerInfo?.name || '',
        email: customerInfo?.email || '',
        contact: customerInfo?.phone || ''
      },
      theme: {
        color: '#3399cc'
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    res.status(500).json({ error: error.message });
  }
});

// Webhook endpoint to handle Razorpay events
app.post('/razorpay-webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  // Verify the webhook signature
  const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET || 'YOUR_WEBHOOK_SECRET';
  const signature = req.headers['x-razorpay-signature'];

  try {
    // Verify the webhook signature
    const payload = req.body.toString();
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(payload)
      .digest('hex');

    const isValid = expectedSignature === signature;

    if (!isValid) {
      console.error('Invalid webhook signature');
      return res.status(400).json({ error: 'Invalid signature' });
    }

    // Parse the webhook payload
    const event = JSON.parse(payload);

    // Handle different event types
    switch (event.event) {
      case 'payment.authorized':
        // Payment has been authorized
        console.log('Payment authorized:', event.payload.payment.entity.id);
        // Process the order
        break;

      case 'payment.captured':
        // Payment has been captured (completed)
        console.log('Payment captured:', event.payload.payment.entity.id);
        // Fulfill the order
        break;

      case 'payment.failed':
        // Payment has failed
        console.log('Payment failed:', event.payload.payment.entity.id);
        // Handle failed payment
        break;

      case 'order.paid':
        // Order has been paid
        console.log('Order paid:', event.payload.order.entity.id);
        // Update order status
        break;

      default:
        console.log(`Unhandled event type: ${event.event}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook Error:', error.message);
    return res.status(400).json({ error: error.message });
  }
});

// Verify Razorpay payment
app.post('/verify-razorpay-payment', async (req, res) => {
  try {
    // Get payment details from request body
    const {
      razorpay_payment_id,
      razorpay_order_id,
      razorpay_signature
    } = req.body;

    console.log('Verifying Razorpay payment:', {
      razorpay_payment_id,
      razorpay_order_id
    });

    // Verify the payment signature
    const generatedSignature = crypto
      .createHmac('sha256', razorpay.key_secret)
      .update(`${razorpay_order_id}|${razorpay_payment_id}`)
      .digest('hex');

    // Check if the generated signature matches the signature from Razorpay
    if (generatedSignature === razorpay_signature) {
      // Payment is successful and verified

      // In a production environment, you would:
      // 1. Update the order status in your database
      // 2. Send confirmation email to the customer
      // 3. Update inventory, etc.

      // Return success response
      res.json({
        success: true,
        message: 'Payment has been verified',
        orderId: razorpay_order_id,
        paymentId: razorpay_payment_id
      });
    } else {
      // Payment verification failed
      res.status(400).json({
        success: false,
        message: 'Payment verification failed'
      });
    }
  } catch (error) {
    console.error('Error verifying Razorpay payment:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get payment details
app.get('/payment/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;

    // Fetch payment details from Razorpay
    const payment = await razorpay.payments.fetch(paymentId);

    res.json({
      success: true,
      payment: payment
    });
  } catch (error) {
    console.error('Error fetching payment details:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get order details
app.get('/order/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    // Fetch order details from Razorpay
    const order = await razorpay.orders.fetch(orderId);

    res.json({
      success: true,
      order: order
    });
  } catch (error) {
    console.error('Error fetching order details:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

const PORT = process.env.PORT || 4242;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
