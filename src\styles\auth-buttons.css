/* Auth Buttons Styles */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap');

.auth-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: nowrap;
    flex-shrink: 0;
}

.login-btn, .signup-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    flex-wrap: nowrap;
    text-decoration: none;
    transition: all 0.3s ease;
    font-family: 'Helvetica Now Display', 'Helvetica', Arial, sans-serif;
}

.login-btn {
    background-color: #e74c3c;
    color: #fff;
    border: 1px solid #fff;
}

.login-btn:hover {
    background-color: #e74d3cb6;
}

.signup-btn {
    background-color: #e74c3c;
    color: #fff;
    border: 1px solid #e74c3c;
    display: flex;
    flex-wrap: nowrap;
}

.signup-btn:hover {
    background-color: #e74d3cb6;
}

/* User dropdown styles */
.user-dropdown {
    position: relative;
    display: inline-block;
}

.user-icon {
    font-size: 24px;
    color: #000000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-icon:hover {
    opacity: 0.8;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #fff;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    border-radius: 4px;
    overflow: hidden;
}

.dropdown-content a {
    color: #333;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-family: 'Helvetica Now Display', 'Helvetica', Arial, sans-serif;
    font-size: 14px;
    transition: all 0.3s ease;
}

.dropdown-content a:hover {
    background-color: #f1f1f1;
}

.show-dropdown {
    display: block;
}

/* Responsive styles */
@media (max-width: 768px) {
    .auth-buttons {
        gap: 5px;
    }

    .login-btn, .signup-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .auth-buttons {
        flex-direction: column;
        gap: 5px;
        position: absolute;
        top: 60px;
        right: 20px;
        background-color: rgba(0, 0, 0, 0.8);
        padding: 10px;
        border-radius: 4px;
        display: none;
    }

    .nav-last:hover .auth-buttons {
        display: flex;
    }
}
