<!DOCTYPE html>
<html>
  <head>
    <title>Stripe Checkout - StepStyle</title>
    <script src="https://js.stripe.com/v3/"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
      body {
        font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        background-color: #f8f9fa;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        color: #333;
      }
      .container {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        padding: 40px;
        text-align: center;
        max-width: 500px;
        width: 90%;
      }
      h1 {
        font-size: 28px;
        margin-bottom: 15px;
        font-weight: 600;
      }
      p {
        font-size: 16px;
        line-height: 1.6;
        color: #666;
        margin-bottom: 30px;
      }
      .checkout-button {
        background-color: #111;
        color: white;
        border: none;
        padding: 14px 30px;
        border-radius: 30px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      .checkout-button:hover {
        background-color: #333;
        transform: translateY(-2px);
      }
      .checkout-button i {
        margin-right: 8px;
        font-size: 20px;
      }
      .back-link {
        display: inline-block;
        margin-top: 20px;
        color: #666;
        text-decoration: none;
        font-size: 14px;
      }
      .back-link:hover {
        color: #111;
        text-decoration: underline;
      }
      .cart-summary {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: left;
      }
      .cart-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
      }
      .cart-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }
      .cart-item img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        margin-right: 15px;
      }
      .cart-item-details {
        flex: 1;
      }
      .cart-item-name {
        font-weight: 500;
        margin-bottom: 5px;
      }
      .cart-item-price {
        color: #666;
        font-size: 14px;
      }
      .cart-total {
        display: flex;
        justify-content: space-between;
        font-weight: 600;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
      }
      .loading {
        display: none;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
      }
      .loading i {
        animation: spin 1s linear infinite;
        font-size: 24px;
        margin-right: 10px;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Complete Your Purchase</h1>
      <p>Review your cart and proceed to secure payment with Stripe.</p>
      
      <div class="cart-summary" id="cart-summary">
        <!-- Cart items will be populated by JavaScript -->
      </div>
      
      <button id="checkout-button" class="checkout-button">
        <i class="ri-bank-card-line"></i>
        Pay Now
      </button>
      
      <div id="loading" class="loading">
        <i class="ri-loader-line"></i>
        <span>Processing your payment...</span>
      </div>
      
      <a href="index.html" class="back-link">← Return to shopping</a>
    </div>

    <script>
      // Initialize Stripe with your publishable key
      const stripe = Stripe('pk_test_51RMCvhFPQAtPKLAIzO3Y7WjVErTuQI2BMKbyLl8uCHiCqBtZuVQEmPCiOOCqB3UnuLhYw2K0s2JnecLutL821Akb00zvhXh8HB');
      
      // Function to get cart items from localStorage
      function getCart() {
        try {
          const storedCart = localStorage.getItem('stepstyle-cart');
          return storedCart ? JSON.parse(storedCart) : [];
        } catch (error) {
          console.error('Error loading cart:', error);
          return [];
        }
      }
      
      // Function to display cart summary
      function displayCartSummary() {
        const cart = getCart();
        const cartSummary = document.getElementById('cart-summary');
        
        if (!cartSummary) return;
        
        if (cart.length === 0) {
          cartSummary.innerHTML = '<p>Your cart is empty.</p>';
          document.getElementById('checkout-button').disabled = true;
          return;
        }
        
        let cartHtml = '';
        let total = 0;
        
        cart.forEach(item => {
          const itemTotal = item.currentPrice * (item.quantity || 1);
          total += itemTotal;
          
          cartHtml += `
            <div class="cart-item">
              <img src="${item.images ? item.images[0] : (item.image || 'assets/images/placeholder.jpg')}" alt="${item.name}">
              <div class="cart-item-details">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">₹${item.currentPrice.toLocaleString('en-IN')} × ${item.quantity || 1}</div>
              </div>
            </div>
          `;
        });
        
        cartHtml += `
          <div class="cart-total">
            <span>Total:</span>
            <span>₹${total.toLocaleString('en-IN')}</span>
          </div>
        `;
        
        cartSummary.innerHTML = cartHtml;
      }
      
      // Display cart summary when page loads
      document.addEventListener('DOMContentLoaded', displayCartSummary);
      
      // Handle checkout button click
      document.getElementById('checkout-button').addEventListener('click', async () => {
        // Show loading indicator
        const loadingElement = document.getElementById('loading');
        const checkoutButton = document.getElementById('checkout-button');
        
        loadingElement.style.display = 'flex';
        checkoutButton.disabled = true;
        
        try {
          // Get cart items
          const cart = getCart();
          
          // Create checkout session
          const response = await fetch('http://localhost:4242/create-checkout-session', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ items: cart }),
          });
          
          const session = await response.json();
          
          // Redirect to Stripe Checkout
          const result = await stripe.redirectToCheckout({
            sessionId: session.id,
          });
          
          if (result.error) {
            // If redirectToCheckout fails due to a browser or network error
            console.error(result.error.message);
            alert('Payment failed: ' + result.error.message);
            
            // Hide loading indicator and re-enable button
            loadingElement.style.display = 'none';
            checkoutButton.disabled = false;
          }
        } catch (error) {
          console.error('Error:', error);
          alert('An error occurred. Please try again.');
          
          // Hide loading indicator and re-enable button
          loadingElement.style.display = 'none';
          checkoutButton.disabled = false;
        }
      });
    </script>
  </body>
</html>
