/**
 * Login & Registration functionality for StepStyle website
 */

import { gsap } from "gsap";

// API URL - Using the existing backend URL
const API_URL = 'http://localhost:4000/api';

// DOM Elements
const tabButtons = document.querySelectorAll('.tab-btn');
const formContents = document.querySelectorAll('.form-content');
const loginForm = document.getElementById('login');
const registerForm = document.getElementById('register');
const togglePasswordButtons = document.querySelectorAll('.toggle-password');
const toast = document.querySelector('.toast-notification');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Set up tab switching
    setupTabs();
    
    // Set up password visibility toggle
    setupPasswordToggle();
    
    // Set up form submissions
    setupFormSubmissions();
    
    // Check if user is already logged in
    checkAuthStatus();
});

/**
 * Set up tab switching functionality
 */
function setupTabs() {
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            formContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(`${tabId}-form`).classList.add('active');
        });
    });
}

/**
 * Set up password visibility toggle
 */
function setupPasswordToggle() {
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordInput = button.previousElementSibling;
            
            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                button.classList.remove('ri-eye-line');
                button.classList.add('ri-eye-off-line');
            } else {
                passwordInput.type = 'password';
                button.classList.remove('ri-eye-off-line');
                button.classList.add('ri-eye-line');
            }
        });
    });
}

/**
 * Set up form submissions
 */
function setupFormSubmissions() {
    // Login form submission
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Get form values
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;
        
        // Validate form
        if (!validateLoginForm(email, password)) {
            return;
        }
        
        try {
            // Send login request to API
            const response = await fetch(`${API_URL}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Save token to localStorage
                localStorage.setItem('auth-token', data.token);
                
                // Show success message
                showToast('Login successful! Redirecting...', 'success');
                
                // Redirect to home page after 1.5 seconds
                setTimeout(() => {
                    // Check if there's a redirect URL in localStorage
                    const redirectUrl = localStorage.getItem('auth-redirect') || '../index.html';
                    localStorage.removeItem('auth-redirect'); // Clear the redirect URL
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                // Show error message
                showToast('User or email and password is wrong', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showToast('An error occurred. Please try again later.', 'error');
        }
    });
    
    // Register form submission
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Get form values
        const name = document.getElementById('register-name').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        
        // Validate form
        if (!validateRegisterForm(name, email, password)) {
            return;
        }
        
        try {
            // Send register request to API
            const response = await fetch(`${API_URL}/user/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, email, password })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Save token to localStorage
                localStorage.setItem('auth-token', data.token);
                
                // Show success message
                showToast('Registration successful! Redirecting...', 'success');
                
                // Redirect to home page after 1.5 seconds
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            } else {
                // Show error message
                showToast(data.message || 'Registration failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showToast('An error occurred. Please try again later.', 'error');
        }
    });
}

/**
 * Validate login form
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {boolean} - Whether form is valid
 */
function validateLoginForm(email, password) {
    let isValid = true;
    
    // Reset error messages
    document.getElementById('login-email-error').textContent = '';
    document.getElementById('login-password-error').textContent = '';
    
    // Validate email
    if (!email) {
        document.getElementById('login-email-error').textContent = 'Email is required';
        isValid = false;
    } else if (!isValidEmail(email)) {
        document.getElementById('login-email-error').textContent = 'Please enter a valid email';
        isValid = false;
    }
    
    // Validate password
    if (!password) {
        document.getElementById('login-password-error').textContent = 'Password is required';
        isValid = false;
    }
    
    return isValid;
}

/**
 * Validate register form
 * @param {string} name - User name
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {boolean} - Whether form is valid
 */
function validateRegisterForm(name, email, password) {
    let isValid = true;
    
    // Reset error messages
    document.getElementById('register-name-error').textContent = '';
    document.getElementById('register-email-error').textContent = '';
    document.getElementById('register-password-error').textContent = '';
    
    // Validate name
    if (!name) {
        document.getElementById('register-name-error').textContent = 'Name is required';
        isValid = false;
    }
    
    // Validate email
    if (!email) {
        document.getElementById('register-email-error').textContent = 'Email is required';
        isValid = false;
    } else if (!isValidEmail(email)) {
        document.getElementById('register-email-error').textContent = 'Please enter a valid email';
        isValid = false;
    }
    
    // Validate password
    if (!password) {
        document.getElementById('register-password-error').textContent = 'Password is required';
        isValid = false;
    } else if (password.length < 8) {
        document.getElementById('register-password-error').textContent = 'Password must be at least 8 characters';
        isValid = false;
    }
    
    return isValid;
}

/**
 * Check if email is valid
 * @param {string} email - Email to validate
 * @returns {boolean} - Whether email is valid
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Show toast notification
 * @param {string} message - Message to display
 * @param {string} type - Toast type (success or error)
 */
function showToast(message, type = 'success') {
    // Set toast message and type
    const toastMessage = document.querySelector('.toast-message');
    toastMessage.textContent = message;
    
    // Remove existing classes and add new type class
    toast.classList.remove('success', 'error');
    toast.classList.add(type);
    
    // Create animation timeline
    const toastTl = gsap.timeline();
    
    // Show toast
    toastTl.to(toast, {
        y: 0,
        opacity: 1,
        duration: 0.5,
        ease: "back.out(1.7)"
    });
    
    // Hide toast after 3 seconds
    toastTl.to(toast, {
        y: 100,
        opacity: 0,
        duration: 0.5,
        ease: "power3.in",
        delay: 3
    });
}

/**
 * Check if user is already authenticated
 */
function checkAuthStatus() {
    const token = localStorage.getItem('auth-token');
    
    if (token) {
        // User is already logged in, redirect to home page
        window.location.href = '../index.html';
    }
}
