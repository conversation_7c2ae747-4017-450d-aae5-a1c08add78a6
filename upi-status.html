<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UPI Payment Status - StepStyle</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
        body {
            font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .status-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .status-icon {
            font-size: 80px;
            margin-bottom: 20px;
        }
        .status-icon.pending {
            color: #FFC107;
        }
        .status-icon.success {
            color: #4CAF50;
        }
        .status-icon.error {
            color: #F44336;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 30px;
        }
        .payment-details {
            background-color: #f1f1f1;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: left;
        }
        .payment-details .detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .payment-details .detail:last-child {
            margin-bottom: 0;
        }
        .payment-details .detail-label {
            font-weight: 500;
            color: #666;
        }
        .payment-details .detail-value {
            font-weight: 600;
            color: #333;
        }
        .button {
            background-color: #111;
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
        }
        .button:hover {
            background-color: #333;
            transform: translateY(-2px);
        }
        .button.secondary {
            background-color: transparent;
            color: #111;
            border: 1px solid #111;
        }
        .button.secondary:hover {
            background-color: #f1f1f1;
        }
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            height: 60px;
        }
    </style>
</head>
<body>
    <div class="status-container" id="status-container">
        <div class="logo">
            <img src="./assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo">
        </div>
        
        <!-- Loading State (Default) -->
        <div id="loading-state" class="loading">
            <div class="spinner"></div>
            <h1>Verifying Payment</h1>
            <p>Please wait while we verify your UPI payment...</p>
        </div>
        
        <!-- Success State (Hidden by default) -->
        <div id="success-state" style="display: none;">
            <i class="ri-checkbox-circle-line status-icon success"></i>
            <h1>Payment Successful!</h1>
            <p>Your UPI payment has been successfully processed. Your order is now being prepared.</p>
            <div class="payment-details" id="success-payment-details">
                <!-- Payment details will be populated by JavaScript -->
            </div>
            <a href="index.html" class="button">Return to Home</a>
        </div>
        
        <!-- Error State (Hidden by default) -->
        <div id="error-state" style="display: none;">
            <i class="ri-error-warning-line status-icon error"></i>
            <h1>Payment Failed</h1>
            <p>We couldn't verify your UPI payment. Please try again or choose a different payment method.</p>
            <div class="payment-details" id="error-payment-details">
                <!-- Payment details will be populated by JavaScript -->
            </div>
            <div>
                <a href="Pages/checkout.html" class="button">Try Again</a>
                <a href="index.html" class="button secondary">Return to Home</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get payment ID from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const paymentId = urlParams.get('paymentId');
            
            if (!paymentId) {
                showErrorState('No payment ID found. Please try again.');
                return;
            }
            
            // Verify payment status
            verifyPayment(paymentId);
        });
        
        async function verifyPayment(paymentId) {
            try {
                // Wait for 3 seconds to simulate processing time
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Call the server to verify payment
                const response = await fetch('http://localhost:4242/verify-upi-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ paymentId }),
                });
                
                const data = await response.json();
                
                if (data.success && data.status === 'completed') {
                    // Payment successful
                    showSuccessState(data);
                    
                    // Clear cart
                    localStorage.removeItem('stepstyle-cart');
                } else {
                    // Payment failed
                    showErrorState('Payment verification failed. Please try again.');
                }
            } catch (error) {
                console.error('Error verifying payment:', error);
                showErrorState('An error occurred while verifying your payment. Please try again.');
            }
        }
        
        function showSuccessState(paymentData) {
            // Hide loading state
            document.getElementById('loading-state').style.display = 'none';
            
            // Show success state
            document.getElementById('success-state').style.display = 'block';
            
            // Populate payment details
            const paymentDetails = document.getElementById('success-payment-details');
            paymentDetails.innerHTML = `
                <div class="detail">
                    <span class="detail-label">Payment ID:</span>
                    <span class="detail-value">${paymentData.paymentId}</span>
                </div>
                <div class="detail">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value">Completed</span>
                </div>
                <div class="detail">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
            `;
        }
        
        function showErrorState(errorMessage) {
            // Hide loading state
            document.getElementById('loading-state').style.display = 'none';
            
            // Show error state
            document.getElementById('error-state').style.display = 'block';
            
            // Populate error details
            const errorDetails = document.getElementById('error-payment-details');
            errorDetails.innerHTML = `
                <div class="detail">
                    <span class="detail-label">Error:</span>
                    <span class="detail-value">${errorMessage}</span>
                </div>
                <div class="detail">
                    <span class="detail-label">Date:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
            `;
        }
    </script>
</body>
</html>
