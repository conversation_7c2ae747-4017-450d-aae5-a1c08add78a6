/**
 * Common authentication functionality for StepStyle website
 * This script handles authentication checks and redirects across all pages
 */

// Import auth UI functions
import { updateAuthUI } from './auth-ui.js';

// Pages that don't require authentication
const publicPages = [
    '/index.html',
    '/',
    '/Pages/login.html'
];

// Check if current page is public
function isPublicPage() {
    const currentPath = window.location.pathname;
    const filename = currentPath.split('/').pop();

    // Index page or root is always public
    if (filename === '' || filename === 'index.html') {
        return true;
    }

    // Login page is public
    if (filename === 'login.html') {
        return true;
    }

    // Check other public pages
    return publicPages.some(page => {
        return currentPath.endsWith(page);
    });
}

// Check if user is authenticated
function isAuthenticated() {
    return localStorage.getItem('auth-token') !== null;
}

// Redirect to login page with return URL
function redirectToLogin() {
    // Get current URL to redirect back after login
    const currentUrl = window.location.href;
    // Encode the URL to use as a parameter
    const encodedReturnUrl = encodeURIComponent(currentUrl);
    // Redirect to login page with return URL
    window.location.href = `/Pages/login.html?returnUrl=${encodedReturnUrl}`;
}

// Main authentication check function
function checkAuth() {
    const isPublic = isPublicPage();
    const isAuth = isAuthenticated();

    console.log('Current page:', window.location.pathname);
    console.log('Is public page:', isPublic);
    console.log('Is authenticated:', isAuth);

    // Update the auth UI based on authentication status
    updateAuthUI();

    // If it's not a public page and user is not authenticated, redirect to login
    if (!isPublic && !isAuth) {
        console.log('Redirecting to login page...');
        redirectToLogin();
    }
}

// Run authentication check when DOM is loaded
document.addEventListener('DOMContentLoaded', checkAuth);

// Export functions for use in other scripts
export { isAuthenticated, redirectToLogin, checkAuth };
