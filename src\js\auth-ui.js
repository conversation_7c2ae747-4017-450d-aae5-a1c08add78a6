/**
 * Authentication UI functionality for StepStyle website
 * This script handles the UI changes based on authentication status
 */

// DOM Elements
let authButtonsContainer;
let userDropdown;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Find the auth buttons container
    authButtonsContainer = document.querySelector('.auth-buttons');
    
    if (authButtonsContainer) {
        // Check if user is authenticated
        updateAuthUI();
        
        // Add event listener for logout
        document.addEventListener('click', handleUserDropdownClick);
    }
});

/**
 * Update the authentication UI based on the user's authentication status
 */
function updateAuthUI() {
    const isAuthenticated = localStorage.getItem('auth-token') !== null;
    
    if (isAuthenticated) {
        // User is authenticated, show user icon with dropdown
        showUserDropdown();
    } else {
        // User is not authenticated, show login/signup buttons
        showAuthButtons();
    }
}

/**
 * Show the login and signup buttons
 */
function showAuthButtons() {
    if (!authButtonsContainer) return;
    
    authButtonsContainer.innerHTML = `
        <a href="/Pages/login.html" class="login-btn">Sign In</a>
        <a href="/Pages/login.html?tab=register" class="signup-btn">Sign Up</a>
    `;
}

/**
 * Show the user icon with dropdown
 */
function showUserDropdown() {
    if (!authButtonsContainer) return;
    
    authButtonsContainer.innerHTML = `
        <div class="user-dropdown">
            <i class="ph ph-user-circle user-icon"></i>
            <div class="dropdown-content">
                <a href="#" id="logout-btn">Logout</a>
            </div>
        </div>
    `;
    
    // Update the reference to the user dropdown
    userDropdown = document.querySelector('.user-dropdown');
    
    // Add event listener for logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
}

/**
 * Handle click on user icon to toggle dropdown
 */
function handleUserDropdownClick(event) {
    // Check if user dropdown exists
    if (!userDropdown) return;
    
    // Check if the click was on the user icon
    const userIcon = userDropdown.querySelector('.user-icon');
    const dropdownContent = userDropdown.querySelector('.dropdown-content');
    
    if (userIcon && userIcon.contains(event.target)) {
        // Toggle dropdown
        dropdownContent.classList.toggle('show-dropdown');
        event.stopPropagation();
    } else if (dropdownContent && !dropdownContent.contains(event.target)) {
        // Close dropdown if click is outside
        dropdownContent.classList.remove('show-dropdown');
    }
}

/**
 * Handle logout button click
 */
function handleLogout(event) {
    event.preventDefault();
    
    // Remove auth token from localStorage
    localStorage.removeItem('auth-token');
    
    // Update UI
    updateAuthUI();
    
    // Show toast notification
    showToast('Logged out successfully!');
    
    // Redirect to home page if on a protected page
    const currentPath = window.location.pathname;
    const filename = currentPath.split('/').pop();
    
    if (filename !== '' && filename !== 'index.html') {
        window.location.href = '/index.html';
    }
}

/**
 * Show toast notification
 * @param {string} message - Message to display
 */
function showToast(message) {
    // Find toast notification element
    const toast = document.querySelector('.toast-notification');
    const toastMessage = document.querySelector('.toast-message');
    
    if (toast && toastMessage) {
        // Set message
        toastMessage.textContent = message;
        
        // Show toast
        toast.style.transform = 'translateY(0)';
        toast.style.opacity = '1';
        
        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateY(100px)';
            toast.style.opacity = '0';
        }, 3000);
    }
}

// Export functions for use in other scripts
export { updateAuthUI, handleLogout };
