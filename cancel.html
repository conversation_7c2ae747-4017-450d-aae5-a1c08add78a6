<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Cancelled - StepStyle</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
        body {
            font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .cancel-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .cancel-icon {
            font-size: 80px;
            color: #f44336;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 30px;
        }
        .back-to-checkout {
            background-color: #111;
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-right: 15px;
        }
        .back-to-home {
            background-color: transparent;
            color: #111;
            border: 1px solid #111;
            padding: 14px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .back-to-checkout:hover {
            background-color: #333;
            transform: translateY(-2px);
        }
        .back-to-home:hover {
            background-color: #f1f1f1;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="cancel-container">
        <i class="ri-close-circle-line cancel-icon"></i>
        <h1>Payment Cancelled</h1>
        <p>Your payment was cancelled. If you experienced any issues during checkout, please try again or contact our customer support.</p>
        <div class="actions">
            <a href="Pages/checkout.html" class="back-to-checkout">Return to Checkout</a>
            <a href="index.html" class="back-to-home">Back to Home</a>
        </div>
    </div>
</body>
</html>
