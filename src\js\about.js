/**
 * About page JavaScript
 * Handles animations and interactions for the About page using GSAP
 */

// Import GSAP and ScrollTrigger
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import '../styles/about.css';
import { checkAuth, isAuthenticated, logout } from './auth-check.js';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

// Set up smooth scrolling with GSAP
gsap.config({
  autoSleep: 60,
  force3D: true,
  nullTargetWarn: false,
});


// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('About page initialized');

  // Check if user is authenticated
  checkAuth();

  // Initialize animations
  initAnimations();

  // Show welcome toast
  showWelcomeToast();

  // Set up user icon click event for login/logout
  const userIcon = document.querySelector('.ph-user-circle');
  if (userIcon) {
    userIcon.addEventListener('click', () => {
      if (isAuthenticated()) {
        // If authenticated, show logout confirmation
        if (confirm('Are you sure you want to logout?')) {
          logout();
        }
      } else {
        // If not authenticated, redirect to login page
        window.location.href = '/Pages/login.html';
      }
    });
  }
});

/**
 * Initialize all animations for the about page
 */
function initAnimations() {
  // Hero section animation
  animateHeroSection();

  // Story section animation
  animateStorySection();

  // Values animation
  animateValuesSection();

  // Founders section animation
  animateFoundersSection();

  // Team members animation
  animateTeamSection();
}

/**
 * Animate the hero section using GSAP
 */
function animateHeroSection() {
  // Create a timeline for hero section animations
  const heroTl = gsap.timeline({
    defaults: {
      ease: "power3.out",
      duration: 1
    }
  });

  // Animate hero title
  heroTl.from('.hero-content h1', {
    y: 50,
    opacity: 0
  });

  // Animate hero subtitle
  heroTl.from('.hero-content p', {
    y: 30,
    opacity: 0,
    delay: 0.2
  }, "-=0.7");
}

/**
 * Animate the story section using GSAP ScrollTrigger
 */
function animateStorySection() {
  // Check if the section exists
  const storySection = document.querySelector('.brand-story');
  if (!storySection) return;

  // Animate story content
  gsap.from('.story-content', {
    x: -50,
    opacity: 0,
    duration: 1,
    scrollTrigger: {
      trigger: '.brand-story',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });

  // Animate story image
  gsap.from('.story-image', {
    x: 50,
    opacity: 0,
    duration: 1,
    scrollTrigger: {
      trigger: '.brand-story',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });
}

/**
 * Animate the values section using GSAP ScrollTrigger
 */
function animateValuesSection() {
  // Check if the section exists
  const valuesSection = document.querySelector('.values-grid');
  if (!valuesSection) return;

  // Animate value items with stagger
  gsap.from('.value-item', {
    y: 30,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    scrollTrigger: {
      trigger: '.values-grid',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });
}

/**
 * Animate the founders section using GSAP ScrollTrigger
 */
function animateFoundersSection() {
  // Check if the section exists
  const foundersSection = document.querySelector('.founders-section');
  if (!foundersSection) return;

  // Animate founders story section
  gsap.from('.founders-story .founders-image', {
    x: -50,
    opacity: 0,
    duration: 1,
    scrollTrigger: {
      trigger: '.founders-story',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });

  gsap.from('.founders-story .founders-content', {
    x: 50,
    opacity: 0,
    duration: 1,
    scrollTrigger: {
      trigger: '.founders-story',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });

  // Animate founder cards with stagger
  gsap.from('.founder-card', {
    y: 50,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    scrollTrigger: {
      trigger: '.founders-grid',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });

  // Animate founder quotes with a slight delay
  gsap.from('.founder-quote', {
    opacity: 0,
    y: 20,
    duration: 0.6,
    stagger: 0.2,
    delay: 0.5,
    scrollTrigger: {
      trigger: '.founders-grid',
      start: 'top 70%',
      toggleActions: 'play none none none'
    }
  });

  // Animate founder social icons with stagger
  gsap.from('.founder-social a', {
    scale: 0,
    opacity: 0,
    duration: 0.4,
    stagger: 0.1,
    delay: 0.8,
    scrollTrigger: {
      trigger: '.founders-grid',
      start: 'top 70%',
      toggleActions: 'play none none none'
    }
  });
}

/**
 * Animate the team section using GSAP ScrollTrigger
 */
function animateTeamSection() {
  // Check if the section exists
  const teamSection = document.querySelector('.team-grid');
  if (!teamSection) return;

  // Animate team members with stagger
  gsap.from('.team-member', {
    y: 50,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    scrollTrigger: {
      trigger: '.team-grid',
      start: 'top 80%',
      end: 'top 50%',
      toggleActions: 'play none none none'
    }
  });
}

/**
 * Show welcome toast notification using GSAP
 */
function showWelcomeToast() {
  const toast = document.querySelector('.toast-notification');

  if (toast) {
    // Create a timeline for toast animation
    const toastTl = gsap.timeline();

    // Show toast with animation
    toastTl.to(toast, {
      y: 0,
      opacity: 1,
      duration: 0.5,
      ease: "back.out(1.7)",
      delay: 1
    });

    // Hide toast after 3 seconds
    toastTl.to(toast, {
      y: 100,
      opacity: 0,
      duration: 0.5,
      ease: "power3.in",
      delay: 3
    });
  }
}

// Add event listeners for CTA buttons with GSAP animations
document.addEventListener('DOMContentLoaded', () => {
  const buttons = document.querySelectorAll('.cta-buttons .btn');

  buttons.forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault();

      // Show different message based on which button was clicked
      const toast = document.querySelector('.toast-notification');
      const toastMessage = document.querySelector('.toast-message');

      if (e.target.classList.contains('primary-btn')) {
        toastMessage.textContent = 'Redirecting to shop...';
      } else {
        toastMessage.textContent = 'Follow us on social media!';
      }

      // Create a timeline for toast animation
      const toastTl = gsap.timeline();

      // Show toast with animation
      toastTl.to(toast, {
        y: 0,
        opacity: 1,
        duration: 0.5,
        ease: "back.out(1.7)"
      });

      // Hide toast after 3 seconds
      toastTl.to(toast, {
        y: 100,
        opacity: 0,
        duration: 0.5,
        ease: "power3.in",
        delay: 3
      });

      // Add button animation
      gsap.to(e.target, {
        scale: 1.05,
        duration: 0.2,
        yoyo: true,
        repeat: 1,
        ease: "power1.inOut"
      });
    });
  });
});
