<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - StepStyle</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <style>
        body {
            font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .success-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .success-icon {
            font-size: 80px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 30px;
        }
        .order-number {
            background-color: #f1f1f1;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 500;
            margin-bottom: 30px;
            display: inline-block;
        }
        .back-to-home {
            background-color: #111;
            color: white;
            border: none;
            padding: 14px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .back-to-home:hover {
            background-color: #333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="success-container">
        <i class="ri-checkbox-circle-line success-icon"></i>
        <h1>Payment Successful!</h1>
        <p>Thank you for your purchase. Your order has been placed successfully and is being processed.</p>
        <div class="order-number" id="order-number">Order #<span id="order-id">ORD-123456789</span></div>
        <a href="index.html" class="back-to-home">Back to Home</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Generate a random order ID for demo purposes
            const orderId = 'ORD-' + Math.floor(Math.random() * 1000000000);
            document.getElementById('order-id').textContent = orderId;
            
            // Clear cart data from localStorage
            localStorage.removeItem('stepstyle-cart');
        });
    </script>
</body>
</html>
